package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 数据库执行任务配置表
 */
@Data
@ToString
@Table("database_execute_config")
public class DatabaseExecuteConfigDO extends AdminCoreDO {

    /** 执行任务名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 数据库连接id<br/>Column: [database_id] */
    @Column(value = "database_id")
    private Long databaseId;

    /** 数据库名,允许为空<br/>Column: [database_name] */
    @Column(value = "database_name")
    private String databaseName;

    /** 任务状态，WAIT待执行，RUNNING执行中，FAILED失败，SUCCESS成功，STOPPED人工停止<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 执行的SQL，该sql中应该包含:keyStart(含) 和 :keyEnd(含) 两个变量占位符，用于每一批次执行时确定范围<br/>Column: [sql] */
    @Column(value = "sql")
    private String sql;

    /** 开始的主键，包含；当它是数字时，程序会自动按数字处理；同时也支持非数字类型的key<br/>Column: [key_start] */
    @Column(value = "key_start")
    private String keyStart;

    /** 结束的主键，包含；当它是数字时，程序会自动按数字处理；同时也支持非数字类型的key<br/>Column: [key_end] */
    @Column(value = "key_end")
    private String keyEnd;

    /** 每次执行的偏移量，即每次执行范围=[当前key,当前key+batch_offset)<br/>Column: [batch_offset] */
    @Column(value = "batch_offset")
    private String batchOffset;

    /** 当前执行的主键值<br/>Column: [current_key_value] */
    @Column(value = "current_key_value")
    private String currentKeyValue;

    /** 定时执行的时间点，当它有值时表示在这个时间点执行<br/>Column: [schedule_at] */
    @Column(value = "schedule_at")
    private LocalDateTime scheduleAt;

    /** 上次执行时间<br/>Column: [last_execute_time] */
    @Column(value = "last_execute_time")
    private LocalDateTime lastExecuteTime;

    /** 错误信息<br/>Column: [error_message] */
    @Column(value = "error_message")
    private String errorMessage;

    /** 创建人id<br/>Column: [create_user_id] */
    @Column(value = "create_user_id")
    private Long createUserId;

    /** 修改人id<br/>Column: [update_user_id] */
    @Column(value = "update_user_id")
    private Long updateUserId;

}